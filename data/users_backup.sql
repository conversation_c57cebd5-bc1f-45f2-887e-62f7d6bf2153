--
-- PostgreSQL database dump
--

-- Dumped from database version 15.13 (Debian 15.13-1.pgdg120+1)
-- Dumped by pg_dump version 15.13 (Debian 15.13-1.pgdg120+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: users; Type: TABLE; Schema: public; Owner: app_user
--

CREATE TABLE public.users (
    id integer NOT NULL,
    uuid uuid,
    email character varying(255) NOT NULL,
    username character varying(100),
    first_name character varying(100),
    last_name character varying(100),
    avatar_url character varying(500),
    password_hash character varying(255),
    is_active boolean NOT NULL,
    is_verified boolean NOT NULL,
    role character varying(20) NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    last_login_at timestamp without time zone,
    email_verification_token character varying(255),
    email_verification_expires timestamp without time zone,
    password_reset_token character varying(255),
    password_reset_expires timestamp without time zone
);


ALTER TABLE public.users OWNER TO app_user;

--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: app_user
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.users_id_seq OWNER TO app_user;

--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: app_user
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: app_user
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: app_user
--

INSERT INTO public.users (id, uuid, email, username, first_name, last_name, avatar_url, password_hash, is_active, is_verified, role, created_at, updated_at, last_login_at, email_verification_token, email_verification_expires, password_reset_token, password_reset_expires) VALUES (2, NULL, '<EMAIL>', NULL, 'Test', 'User', NULL, '$2b$12$XCiXZOp0dGmCNmro0oekv.l4O36Xcw60U0xdoGaJcd/JR8V4.o/cO', true, false, 'user', '2025-06-24 12:05:27', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO public.users (id, uuid, email, username, first_name, last_name, avatar_url, password_hash, is_active, is_verified, role, created_at, updated_at, last_login_at, email_verification_token, email_verification_expires, password_reset_token, password_reset_expires) VALUES (1, NULL, '<EMAIL>', NULL, 'Pankaj', 'Kumar', NULL, '$2b$12$OsgGAg.WxCM5LwYJ7/urPeTLHZbU73U1AfQGN6cFvabve7r/FH4Cq', true, false, 'user', '2025-06-23 15:38:57', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO public.users (id, uuid, email, username, first_name, last_name, avatar_url, password_hash, is_active, is_verified, role, created_at, updated_at, last_login_at, email_verification_token, email_verification_expires, password_reset_token, password_reset_expires) VALUES (3, NULL, '<EMAIL>', NULL, 'Test', NULL, NULL, '$2b$12$R74d.S.5Jve8V.N4D5voeeni4LTmEXfSkxYSquoGmOP6gg8bk2Iqy', true, false, 'user', '2025-07-30 08:32:59', '2025-09-06 07:03:57.521497', '2025-09-06 07:03:57.52097', NULL, NULL, NULL, NULL);


--
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.users_id_seq', 1, false);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: app_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: ix_users_email; Type: INDEX; Schema: public; Owner: app_user
--

CREATE UNIQUE INDEX ix_users_email ON public.users USING btree (email);


--
-- Name: ix_users_id; Type: INDEX; Schema: public; Owner: app_user
--

CREATE INDEX ix_users_id ON public.users USING btree (id);


--
-- Name: ix_users_username; Type: INDEX; Schema: public; Owner: app_user
--

CREATE UNIQUE INDEX ix_users_username ON public.users USING btree (username);


--
-- Name: ix_users_uuid; Type: INDEX; Schema: public; Owner: app_user
--

CREATE UNIQUE INDEX ix_users_uuid ON public.users USING btree (uuid);


--
-- PostgreSQL database dump complete
--

