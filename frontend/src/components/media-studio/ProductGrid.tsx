import React, { useState, useMemo } from "react";
import { cn } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Copy,
  Filter,
  Image,
  Video,
  Wand2,
  CheckCircle,
  AlertCircle,
  Search,
} from "lucide-react";
import { Product, Asset, MainTab } from "@/types/mediaStudio";
import { PromptEditor } from "./PromptEditor";
import { Thumbnail } from "./Thumbnail";
import { CollectionsFilter } from "./CollectionsFilter";

interface ProductGridProps {
  products: Product[];
  selectedAssetIds: Set<string>;
  onAssetSelect: (asset: Asset, isMultiSelect: boolean) => void;
  prompts: Record<string, string>;
  onPromptChange: (productId: string, value: string) => void;
  onTabChange: (tab: MainTab) => void;
  selectedProductIds: Set<string>;
  onProductSelectionChange: (productId: string, isChecked: boolean) => void;
  onSelectAllProducts: (isChecked: boolean) => void;
  onCopyPromptToAll: (sourceProductId: string) => void;
  generatedImages?: Record<string, Asset[]>;
  availableCollections?: Array<{ id: string; name: string; color: string }>;
  collectionFilters?: string[];
  onCollectionFiltersChange?: (filters: string[]) => void;
  searchQuery?: string;
  onSearchQueryChange?: (query: string) => void;
  filterSelectedOnly?: boolean;
  onFilterSelectedOnlyChange?: (value: boolean) => void;
  filterHasGenerated?: boolean;
  onFilterHasGeneratedChange?: (value: boolean) => void;
  sortMode?: "default" | "selected_first" | "generated_first";
  onSortModeChange?: (
    mode: "default" | "selected_first" | "generated_first"
  ) => void;
  productTotalCount?: number;
}

const ProductRow: React.FC<{
  product: Product;
  products: Product[];
  selectedAssetIds: Set<string>;
  onAssetSelect: (asset: Asset, isMultiSelect: boolean) => void;
  prompts: Record<string, string>;
  onPromptChange: (productId: string, value: string) => void;
  onTabChange: (tab: MainTab) => void;
  selectedProductIds: Set<string>;
  onProductSelectionChange: (productId: string, isChecked: boolean) => void;
  onCopyPromptToAll: (sourceProductId: string) => void;
  generatedImages?: Record<string, Asset[]>;
  failedPlaceholderIds?: Set<string>;
  allAvailableAssets?: Asset[];
}> = ({
  product,
  products,
  selectedAssetIds,
  onAssetSelect,
  prompts,
  onPromptChange,
  onTabChange,
  selectedProductIds,
  onProductSelectionChange,
  onCopyPromptToAll,
  generatedImages = {},
  failedPlaceholderIds = new Set(),
  allAvailableAssets = [],
}) => {
  const isRowSelected = selectedProductIds.has(product.id);
  const hasPlaceholder = React.useMemo(
    () => product.assets.some((a) => a.id.startsWith("temp_")),
    [product.assets]
  );

  const existingAssetIds = React.useMemo(
    () => new Set(product.assets.map((a) => a.id)),
    [product.assets]
  );

  const productGeneratedImages = generatedImages[product.id] || [];
  const filteredGeneratedImages = React.useMemo(
    () => productGeneratedImages.filter((a) => !existingAssetIds.has(a.id)),
    [productGeneratedImages, existingAssetIds]
  );

  // Get all available assets for this product (original + generated)
  const allProductAssets = React.useMemo(() => {
    return [...product.assets, ...filteredGeneratedImages];
  }, [product.assets, filteredGeneratedImages]);

  return (
    <div
      className={cn(
        "group border border-gray-200 dark:border-gray-700 rounded-xl bg-white dark:bg-gray-800/50 backdrop-blur-sm transition-all duration-200 hover:shadow-md hover:border-gray-300 dark:hover:border-gray-600",
        isRowSelected
          ? "ring-2 ring-primary/30 border-primary/50 shadow-lg"
          : "",
        hasPlaceholder ? "animate-pulse" : ""
      )}
    >
      <div className="grid grid-cols-12 gap-0">
        {/* Column 1: Product Info */}
        <div className="col-span-4 p-4 border-r border-gray-100 dark:border-gray-700/50">
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-gray-900 dark:text-gray-100 text-sm leading-tight mb-1 truncate">
                {product.title}
              </h3>
            </div>
            <Checkbox
              checked={isRowSelected}
              onCheckedChange={(checked) =>
                onProductSelectionChange(product.id, checked as boolean)
              }
              className="mt-0.5 w-4 h-4 border-2 border-gray-300 dark:border-gray-600 data-[state=checked]:bg-primary data-[state=checked]:border-primary"
            />
          </div>

          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1">
              {allProductAssets.length > 0 && (
                <div className="flex items-center gap-1">
                  {allProductAssets.slice(0, 2).map((asset, idx) => (
                    <div
                      key={asset.id}
                      className={cn(
                        "w-4 h-4 rounded border flex items-center justify-center text-[10px]",
                        asset.type === "image"
                          ? "bg-blue-50 border-blue-200 text-blue-600"
                          : "bg-purple-50 border-purple-200 text-purple-600"
                      )}
                    >
                      {asset.type === "image" ? (
                        <Image className="w-2.5 h-2.5" />
                      ) : (
                        <Video className="w-2.5 h-2.5" />
                      )}
                    </div>
                  ))}
                  {allProductAssets.length > 2 && (
                    <span className="text-xs text-gray-500 font-medium">
                      +{allProductAssets.length - 2}
                    </span>
                  )}
                </div>
              )}
            </div>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {allProductAssets.length} asset
              {allProductAssets.length !== 1 ? "s" : ""}
            </span>
          </div>

          {/* Categories */}
          {(product.collections ?? []).length > 0 && (
            <div className="flex flex-wrap gap-1.5 mb-4">
              {(product.collections ?? []).slice(0, 3).map((col) => (
                <Badge
                  key={col.id}
                  variant="secondary"
                  className="text-xs px-2 py-0.5 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-0"
                >
                  {col.name}
                </Badge>
              ))}
              {(product.collections ?? []).length > 3 && (
                <Badge
                  variant="secondary"
                  className="text-xs px-2 py-0.5 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-0"
                >
                  +{(product.collections ?? []).length - 3}
                </Badge>
              )}
            </div>
          )}

          {/* Product Media Thumbnails */}
          {allProductAssets.length > 0 && (
            <div className="grid grid-cols-4 gap-2 mb-4">
              {allProductAssets.slice(0, 6).map((asset) => (
                <div
                  key={asset.id}
                  className={cn(
                    "relative aspect-square border rounded-md cursor-pointer transition-all duration-200 overflow-hidden",
                    selectedAssetIds.has(asset.id)
                      ? "border-primary ring-1 ring-primary/20 shadow-sm"
                      : "border-gray-200 dark:border-gray-600 hover:border-primary/50 hover:shadow-sm"
                  )}
                  onClick={() => onAssetSelect(asset, true)}
                >
                  {asset.type === "image" ? (
                    <img
                      src={asset.url}
                      alt={asset.filename}
                      className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
                      onError={(e) => {
                        e.currentTarget.src =
                          "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMiAxMk0xMiAxMnoiIHN0cm9rZT0iIzk3OTdhNyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+";
                      }}
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 flex items-center justify-center">
                      <Video className="w-3 h-3 text-gray-500 dark:text-gray-400" />
                    </div>
                  )}

                  {/* Selection indicator */}
                  {selectedAssetIds.has(asset.id) && (
                    <div className="absolute -top-0.5 -right-0.5 w-4 h-4 bg-primary rounded-full flex items-center justify-center shadow-sm">
                      <CheckCircle className="w-2.5 h-2.5 text-white" />
                    </div>
                  )}

                  {/* Generation indicator */}
                  {asset.id.startsWith("temp_") && (
                    <div className="absolute inset-0 bg-primary/10 flex items-center justify-center">
                      <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                    </div>
                  )}
                </div>
              ))}
              {allProductAssets.length > 6 && (
                <div className="aspect-square border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-md flex items-center justify-center bg-gray-50 dark:bg-gray-800/50">
                  <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                    +{allProductAssets.length - 6}
                  </span>
                </div>
              )}
            </div>
          )}

          {/* Action buttons */}
          <div className="flex items-center justify-between pt-2 border-t border-gray-100 dark:border-gray-700/50">
            <div className="flex items-center gap-2">
              {productGeneratedImages.length > 0 && (
                <div className="flex items-center gap-1 px-2 py-1 bg-green-50 dark:bg-green-900/20 rounded-md">
                  <Wand2 className="w-3 h-3 text-green-600 dark:text-green-400" />
                  <span className="text-xs text-green-700 dark:text-green-300 font-medium">
                    {productGeneratedImages.length} generated
                  </span>
                </div>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onCopyPromptToAll(product.id)}
              className="text-xs h-7 px-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700"
              title="Copy prompt to all products"
            >
              <Copy className="w-3 h-3" />
            </Button>
          </div>
        </div>

        {/* Column 2: Prompt Box */}
        <div className="col-span-7 p-5 border-r border-gray-100 dark:border-gray-700/50">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Generation Prompt
              </label>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {prompts[product.id]?.length || 0} characters
              </div>
            </div>
            <PromptEditor
              value={prompts[product.id] || ""}
              onChange={(value) => onPromptChange(product.id, value)}
              placeholder="Describe the image or video you want to create for this product..."
              availableAssets={allAvailableAssets}
              className="min-h-[120px] border-gray-200 dark:border-gray-600 focus-within:border-primary focus-within:ring-1 focus-within:ring-primary/20"
            />
          </div>
        </div>

        {/* Column 3: Add-ons & Actions */}
        <div className="col-span-1 p-2">
          <div className="space-y-3">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Enhancements
            </div>
            <div className="space-y-2">
              <button
                type="button"
                onClick={() => onTabChange("models")}
                className="w-full flex items-center px-3 py-2 text-xs border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 hover:border-primary/50 hover:bg-primary/5 transition-colors"
              >
                + Models
              </button>
              <button
                type="button"
                onClick={() => onTabChange("props")}
                className="w-full flex items-center px-3 py-2 text-xs border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 hover:border-primary/50 hover:bg-primary/5 transition-colors"
              >
                + Props
              </button>
              <button
                type="button"
                onClick={() => onTabChange("scenes")}
                className="w-full flex items-center px-3 py-2 text-xs border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 hover:border-primary/50 hover:bg-primary/5 transition-colors"
              >
                + Scenes
              </button>
            </div>

            {/* Status indicators */}
            <div className="pt-3 border-t border-gray-100 dark:border-gray-700/50">
              <div className="flex items-center gap-2">
                {hasPlaceholder ? (
                  <div className="flex items-center gap-2 text-amber-600 dark:text-amber-400">
                    <div className="w-2 h-2 bg-amber-500 rounded-full animate-pulse"></div>
                    <span className="text-xs font-medium">Generating...</span>
                  </div>
                ) : productGeneratedImages.length > 0 ? (
                  <div className="flex items-center gap-2 text-green-600 dark:text-green-400">
                    <CheckCircle className="w-3 h-3" />
                    <span className="text-xs font-medium">Ready</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2 text-gray-500 dark:text-gray-400">
                    <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                    <span className="text-xs font-medium">Draft</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export const ProductGrid: React.FC<ProductGridProps> = ({
  products,
  selectedAssetIds,
  onAssetSelect,
  prompts,
  onPromptChange,
  onTabChange,
  selectedProductIds,
  onProductSelectionChange,
  onSelectAllProducts,
  onCopyPromptToAll,
  generatedImages = {},
  availableCollections = [],
  collectionFilters = [],
  onCollectionFiltersChange,
  searchQuery = "",
  onSearchQueryChange,
  filterSelectedOnly = false,
  onFilterSelectedOnlyChange,
  filterHasGenerated = false,
  onFilterHasGeneratedChange,
  sortMode = "default",
  onSortModeChange,
  productTotalCount,
}) => {
  const [filtersOpen, setFiltersOpen] = useState(false);

  // Computed values
  const isClientFilterActive =
    filterSelectedOnly || filterHasGenerated || collectionFilters.length > 0;
  const areAllProductsSelected =
    products.length > 0 && products.every((p) => selectedProductIds.has(p.id));

  return (
    <div className="w-full h-full bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden shadow-sm">
      {/* Grid Layout */}
      <div className="overflow-auto max-h-[calc(100vh-400px)] p-6">
        {products.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-16 text-center">
            <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
              <Search className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              No products found
            </h3>
            <p className="text-gray-500 dark:text-gray-400 max-w-md">
              {searchQuery || collectionFilters.length > 0
                ? "Try adjusting your search or filters to find more products."
                : "Add some products to get started with media generation."}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {products.map((product) => (
              <ProductRow
                key={product.id}
                product={product}
                products={products}
                selectedAssetIds={selectedAssetIds}
                onAssetSelect={onAssetSelect}
                prompts={prompts}
                onPromptChange={onPromptChange}
                onTabChange={onTabChange}
                selectedProductIds={selectedProductIds}
                onProductSelectionChange={onProductSelectionChange}
                onCopyPromptToAll={onCopyPromptToAll}
                generatedImages={generatedImages}
                failedPlaceholderIds={new Set()}
                allAvailableAssets={[]}
              />
            ))}
          </div>
        )}
      </div>

      {/* Spacer to prevent the floating bar from obscuring the last row */}
      <div className="h-40 flex-shrink-0" />
    </div>
  );
};
