import React from "react";
import { MainTab } from "@/types/mediaStudio";
import { cn } from "@/lib/utils";

interface MainTabsProps {
  activeTab: MainTab;
  onTabChange: (tab: MainTab) => void;
}

export const MainTabs: React.FC<MainTabsProps> = ({ activeTab, onTabChange }) => {
  const tabs: Array<{ id: MainTab; label: string }> = [
    { id: "canvas", label: "Gallery" },
    { id: "models", label: "Models" },
    { id: "props", label: "Props" },
    { id: "scenes", label: "Scenes" },
    { id: "brandbook", label: "Brandbook" },
  ];

  return (
    <div className="flex-shrink-0 w-48 bg-white dark:bg-gray-900 border-l border-gray-200 dark:border-gray-700 flex flex-col">
      {tabs.map(({ id, label }) => (
        <button
          key={id}
          onClick={() => onTabChange(id)}
          className={cn(
            "px-4 py-3 text-sm font-medium transition-colors cursor-pointer text-left border-b border-gray-100 dark:border-gray-800",
            activeTab === id
              ? "text-primary bg-blue-50 dark:bg-blue-900/20 border-r-2 border-primary"
              : "text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800"
          )}
        >
          {label}
        </button>
      ))}
    </div>
  );
};