import React from "react";
import { Asset } from "@/services/mediaService";
import { Thumbnail } from "./Thumbnail";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { RefreshCw } from "lucide-react";
import { mediaService } from "@/services/mediaService";

export const PreviewPane: React.FC = () => {
  const [assets, setAssets] = React.useState<Asset[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [nextCursor, setNextCursor] = React.useState<string | null>(null);
  const [isLoadingMore, setIsLoadingMore] = React.useState(false);
  const [typeFilter, setTypeFilter] = React.useState<"all" | "image" | "video">("all");
  const [promptQuery, setPromptQuery] = React.useState<string>("");
  const [debouncedQuery, setDebouncedQuery] = React.useState<string>("");

  // Debounce query to reduce recompute churn
  React.useEffect(() => {
    const t = window.setTimeout(
      () => setDebouncedQuery(promptQuery.trim().toLowerCase()),
      200
    );
    return () => window.clearTimeout(t);
  }, [promptQuery]);

  // Initial load
  React.useEffect(() => {
    let cancelled = false;
    const load = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await mediaService.getGeneratedAssets({ limit: 60 });
        if (cancelled) return;
        setAssets(response.items);
        setNextCursor(response.next_cursor || null);
      } catch (e: any) {
        if (cancelled) return;
        setError(
          typeof e?.message === "string"
            ? `Failed to load: ${e.message}`
            : "Failed to load gallery"
        );
        setAssets([]);
        setNextCursor(null);
      } finally {
        if (!cancelled) setLoading(false);
      }
    };
    load();
    return () => {
      cancelled = true;
    };
  }, []);

  // Load more via cursor
  const loadMore = React.useCallback(async () => {
    if (!nextCursor || isLoadingMore) return;
    setIsLoadingMore(true);
    try {
      const response = await mediaService.getGeneratedAssets({
        limit: 60,
        cursor: nextCursor
      });
      setAssets((prev) => [...prev, ...response.items]);
      setNextCursor(response.next_cursor || null);
    } catch {
      // swallow; UI will keep current items
    } finally {
      setIsLoadingMore(false);
    }
  }, [nextCursor, isLoadingMore]);

  // Bottom sentinel for auto "infinite scroll"
  const sentinelRef = React.useRef<HTMLDivElement | null>(null);
  React.useEffect(() => {
    if (!sentinelRef.current) return;
    const el = sentinelRef.current;
    const io = new IntersectionObserver(
      (entries) => {
        const e = entries[0];
        if (e && e.isIntersecting) {
          // Trigger load-more when sentinel enters view
          if (nextCursor && !isLoadingMore) {
            void loadMore();
          }
        }
      },
      { root: null, rootMargin: "600px 0px 0px 0px", threshold: 0 }
    );
    io.observe(el);
    return () => io.disconnect();
  }, [loadMore, nextCursor, isLoadingMore]);

  const filteredAssets = React.useMemo(() => {
    const q = debouncedQuery;
    return assets.filter((a) => {
      const typeOk = typeFilter === "all" || a.type === typeFilter;
      const text = `${a.prompt || ""} ${a.filename || ""}`.toLowerCase();
      const queryOk = q === "" || text.includes(q);
      return typeOk && queryOk;
    });
  }, [assets, typeFilter, debouncedQuery]);

  const onSelect = React.useCallback(() => {}, []);

  const refreshGallery = React.useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await mediaService.getGeneratedAssets({ limit: 60 });
      setAssets(response.items);
      setNextCursor(response.next_cursor || null);
    } catch (e: any) {
      setError(
        typeof e?.message === "string"
          ? `Failed to load: ${e.message}`
          : "Failed to load gallery"
      );
      setAssets([]);
      setNextCursor(null);
    } finally {
      setLoading(false);
    }
  }, []);

  return (
    <div className="w-full h-full flex-1 self-stretch overflow-auto p-4 lg:p-6 bg-transparent">
      {loading ? (
        <div className="h-full min-h-[200px] flex items-center justify-center text-gray-500 dark:text-gray-400 text-sm">
          <RefreshCw className="h-6 w-6 animate-spin mr-2" />
          Loading…
        </div>
      ) : error ? (
        <div className="h-full min-h-[200px] flex items-center justify-center text-amber-700 dark:text-amber-300 text-sm">
          {error}
        </div>
      ) : assets.length === 0 ? (
        <div className="h-full min-h-[200px] flex items-center justify-center text-gray-500 dark:text-gray-400 text-sm">
          No generated assets yet.
        </div>
      ) : (
        <>
          {/* Filters toolbar */}
          <div className="mb-3 flex items-center gap-2 flex-wrap">
            {/* Type tags */}
            <button
              type="button"
              onClick={() => setTypeFilter("all")}
              className={`px-2.5 py-1 text-xs rounded-full border ${
                typeFilter === "all"
                  ? "bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600"
                  : "bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 border-gray-300 dark:border-gray-600"
              }`}
              aria-pressed={typeFilter === "all"}
              title="All media"
            >
              All
            </button>
            <button
              type="button"
              onClick={() => setTypeFilter("image")}
              className={`px-2.5 py-1 text-xs rounded-full border ${
                typeFilter === "image"
                  ? "bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600"
                  : "bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 border-gray-300 dark:border-gray-600"
              }`}
              aria-pressed={typeFilter === "image"}
              title="Images"
            >
              Images
            </button>
            <button
              type="button"
              onClick={() => setTypeFilter("video")}
              className={`px-2.5 py-1 text-xs rounded-full border ${
                typeFilter === "video"
                  ? "bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600"
                  : "bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 border-gray-300 dark:border-gray-600"
              }`}
              aria-pressed={typeFilter === "video"}
              title="Videos"
            >
              Videos
            </button>

            {/* Search input */}
            <div className="flex-1 max-w-xs">
              <Input
                type="text"
                placeholder="Search prompts..."
                value={promptQuery}
                onChange={(e) => setPromptQuery(e.target.value)}
                className="h-7 text-xs"
              />
            </div>

            {/* Refresh button */}
            <Button
              variant="outline"
              size="sm"
              onClick={refreshGallery}
              disabled={loading}
              className="h-7 px-2"
            >
              <RefreshCw className={`h-3 w-3 ${loading ? 'animate-spin' : ''}`} />
            </Button>

            {/* Count tag aligned right */}
            <span className="ml-auto inline-flex items-center rounded-full bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-100 px-2.5 py-0.5 text-[11px] font-medium">
              {filteredAssets.length} items
            </span>
          </div>

          {/* Simple Grid */}
          <div className="relative h-full w-full overflow-auto">
            <div className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 pb-10">
              {filteredAssets.map((asset) => (
                <div key={asset.id} className="w-full aspect-square">
                  <Thumbnail
                    asset={asset}
                    isSelected={false}
                    onSelect={onSelect}
                    isGenerated
                  />
                </div>
              ))}
            </div>

            {/* Bottom sentinel for auto-load */}
            <div ref={sentinelRef} className="w-full h-10" />
            {isLoadingMore && (
              <div className="absolute bottom-2 left-1/2 -translate-x-1/2 text-xs text-gray-500 dark:text-gray-400">
                <RefreshCw className="h-4 w-4 animate-spin inline mr-1" />
                Loading more…
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};